/**
 * Cache utility functions for environment-specific cache keys
 */

/**
 * Generates an environment-specific cache key
 * @param baseKey - The base cache key
 * @returns Environment-prefixed cache key (unless in production)
 */
export function generateCacheKey(baseKey: string): string {
  const environment = process.env.ENV;
  
  // In production, use the base key as-is
  if (environment === 'prod') {
    return baseKey;
  }
  
  // For all other environments, prefix with environment
  if (environment) {
    return `${environment}:${baseKey}`;
  }
  
  // Fallback if ENV is not set (shouldn't happen in normal operation)
  return `dev:${baseKey}`;
}

/**
 * Cache key patterns for different services
 */
export const CacheKeyPatterns = {
  // User cache keys
  USER_BY_SUB: (sub: string) => generateCacheKey(`user:sub:${sub}`),
  
  // Drone authorization cache keys
  DRONE_AUTH_BY_DRONE_ID: (droneId: string) => generateCacheKey(`drone:auth:${droneId}`),
  DRONE_AUTH_BY_ID: (id: string) => generateCacheKey(`drone:auth:id:${id}`),
  DRONE_AUTH_LATEST: (droneId: string) => generateCacheKey(`drone:auth:latest:${droneId}`),
  
  // Service zone cache keys
  SERVICE_ZONE_BY_ID: (id: string) => generateCacheKey(`servicezone:id:${id}`),
  SERVICE_ZONE_BY_H3: (h3Indexes: string[]) => {
    const sortedIndexes = h3Indexes.sort();
    return generateCacheKey(`servicezone:h3:${sortedIndexes.join(':')}`);
  },
  
  // Organization cache keys (already implemented)
  ORG_BY_AUTH0_ID: (auth0Id: string) => generateCacheKey(`org:auth0:${auth0Id}`),
} as const;
