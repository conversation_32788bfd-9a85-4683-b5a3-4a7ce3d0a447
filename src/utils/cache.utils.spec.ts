import { generateCacheKey, CacheKeyPatterns } from './cache.utils';

describe('Cache Utils', () => {
  const originalEnv = process.env.ENV;

  afterEach(() => {
    process.env.ENV = originalEnv;
  });

  describe('generateCacheKey', () => {
    it('should return base key for production environment', () => {
      process.env.ENV = 'prod';
      const result = generateCacheKey('user:sub:123');
      expect(result).toBe('user:sub:123');
    });

    it('should prefix key with environment for dev environment', () => {
      process.env.ENV = 'dev';
      const result = generateCacheKey('user:sub:123');
      expect(result).toBe('dev:user:sub:123');
    });

    it('should prefix key with environment for staging environment', () => {
      process.env.ENV = 'staging';
      const result = generateCacheKey('user:sub:123');
      expect(result).toBe('staging:user:sub:123');
    });

    it('should default to dev prefix when ENV is not set', () => {
      delete process.env.ENV;
      const result = generateCacheKey('user:sub:123');
      expect(result).toBe('dev:user:sub:123');
    });
  });

  describe('CacheKeyPatterns', () => {
    beforeEach(() => {
      process.env.ENV = 'dev';
    });

    it('should generate correct user cache key', () => {
      const result = CacheKeyPatterns.USER_BY_SUB('auth0|123');
      expect(result).toBe('dev:user:sub:auth0|123');
    });

    it('should generate correct drone auth cache key', () => {
      const result = CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID('drone123');
      expect(result).toBe('dev:drone:auth:drone123');
    });

    it('should generate correct service zone cache key', () => {
      const result = CacheKeyPatterns.SERVICE_ZONE_BY_H3(['h3index1', 'h3index2']);
      expect(result).toBe('dev:servicezone:h3:h3index1:h3index2');
    });

    it('should generate correct organization cache key', () => {
      const result = CacheKeyPatterns.ORG_BY_AUTH0_ID('org123');
      expect(result).toBe('dev:org:auth0:org123');
    });
  });

  describe('Production Environment', () => {
    beforeEach(() => {
      process.env.ENV = 'prod';
    });

    it('should not prefix keys in production', () => {
      const userKey = CacheKeyPatterns.USER_BY_SUB('auth0|123');
      const droneKey = CacheKeyPatterns.DRONE_AUTH_BY_DRONE_ID('drone123');
      const serviceZoneKey = CacheKeyPatterns.SERVICE_ZONE_BY_H3(['h3index1']);
      const orgKey = CacheKeyPatterns.ORG_BY_AUTH0_ID('org123');

      expect(userKey).toBe('user:sub:auth0|123');
      expect(droneKey).toBe('drone:auth:drone123');
      expect(serviceZoneKey).toBe('servicezone:h3:h3index1');
      expect(orgKey).toBe('org:auth0:org123');
    });
  });
});
